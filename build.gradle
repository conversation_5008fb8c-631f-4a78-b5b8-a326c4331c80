plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.0'
	id 'io.spring.dependency-management' version '1.1.6'
}

group = 'com.afs'
version = '0.0.1-SNAPSHOT'

java {
	sourceCompatibility = '17'
}

repositories {
    mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'

    implementation 'org.commonmark:commonmark:0.25.1'

    implementation "org.springframework.ai:spring-ai-starter-model-openai:1.0.1"
    implementation 'org.springframework.ai:spring-ai-starter-mcp-server:1.0.1'
    /// https://docs.spring.io/spring-ai/reference/api/mcp/mcp-server-boot-starter-docs.html
    implementation 'org.springframework.ai:spring-ai-starter-mcp-server-webmvc:1.0.1'

	runtimeOnly 'com.h2database:h2'
	runtimeOnly 'com.mysql:mysql-connector-j'
}

tasks.named('test') {
	useJUnitPlatform()
}
